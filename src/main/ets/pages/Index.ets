// 华为手表表盘主页面
@Entry
@Component
struct Index {
  @State currentTime: string = ''
  @State currentDate: string = ''
  private timer: number = -1

  aboutToAppear() {
    this.updateTime()
    this.timer = setInterval(() => {
      this.updateTime()
    }, 1000)
  }

  aboutToDisappear() {
    if (this.timer !== -1) {
      clearInterval(this.timer)
    }
  }

  updateTime() {
    const now = new Date()
    this.currentTime = now.toLocaleTimeString('zh-CN', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    })
    this.currentDate = now.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      weekday: 'short'
    })
  }

  build() {
    Column() {
      // 时间显示
      Text(this.currentTime)
        .fontSize(48)
        .fontColor(Color.White)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 60 })

      // 日期显示
      Text(this.currentDate)
        .fontSize(16)
        .fontColor('#CCCCCC')
        .margin({ top: 10 })

      // 装饰性元素
      Row() {
        Circle({ width: 8, height: 8 })
          .fill('#FF6B6B')
        Circle({ width: 8, height: 8 })
          .fill('#4ECDC4')
          .margin({ left: 10 })
        Circle({ width: 8, height: 8 })
          .fill('#45B7D1')
          .margin({ left: 10 })
      }
      .margin({ top: 30 })

      Spacer()

      // 底部信息
      Text('华为手表')
        .fontSize(12)
        .fontColor('#888888')
        .margin({ bottom: 20 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#000000')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }
}
