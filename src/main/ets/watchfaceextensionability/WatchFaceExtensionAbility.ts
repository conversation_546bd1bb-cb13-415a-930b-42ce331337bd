import ExtensionAbility from '@ohos.app.ability.ExtensionAbility';
import hilog from '@ohos.hilog';

export default class WatchFaceExtensionAbility extends ExtensionAbility {
  onCreate(want) {
    hilog.info(0x0000, 'testTag', '%{public}s', 'WatchFaceExtensionAbility onCreate');
  }

  onDestroy() {
    hilog.info(0x0000, 'testTag', '%{public}s', 'WatchFaceExtensionAbility onDestroy');
  }

  onRequest(want, startId) {
    hilog.info(0x0000, 'testTag', '%{public}s', 'WatchFaceExtensionAbility onRequest');
  }

  // 表盘预览
  onWallpaperChange(wallpaperType: number) {
    hilog.info(0x0000, 'testTag', 'WatchFaceExtensionAbility onWallpaperChange: %{public}d', wallpaperType);
  }

  // 表盘设置
  onWallpaperColorsChange(colors: Array<number>) {
    hilog.info(0x0000, 'testTag', 'WatchFaceExtensionAbility onWallpaperColorsChange');
  }
}
